# Daily Admin Notice Addon for Pterodactyl Panel

A professional addon that allows administrators to display important notices, announcements, and status updates to users on their dashboard. Perfect for hosting providers who need to communicate maintenance schedules, service updates, or important information to their users.

## Features

- **Dashboard Notice Bar**: Prominent notice display on user dashboard
- **Admin Management Interface**: Full CRUD interface for managing notices
- **Multiple Notice Types**: Info, Success, Warning, and Danger styling
- **Role-Based Targeting**: Show notices to specific user roles (admin/user) or all users
- **Dismissible Notices**: Users can dismiss notices (optional per notice)
- **Scheduled Notices**: Set start and end dates for automatic notice display
- **Rich Content Support**: HTML content support for formatted notices
- **Responsive Design**: Mobile-friendly interface matching Pterodactyl's design
- **API Integration**: Full REST API support for programmatic management

## Screenshots

### User Dashboard with Notice Bar
The notice bar appears prominently at the top of the user dashboard, styled according to the notice type.

### Admin Management Interface
- **Notice List**: View all notices with status, type, and creation information
- **Create/Edit Forms**: Comprehensive forms for notice management
- **Dismissal Tracking**: See which users have dismissed each notice

## Installation

### Prerequisites
- Pterodactyl Panel v1.0+ (tested with current version)
- PHP 8.1+
- MySQL/MariaDB database
- Composer
- Node.js and npm (for frontend compilation)

### Step 1: Download and Extract Files

1. Download the addon files
2. Extract to your Pterodactyl Panel root directory (`/var/www/pterodactyl` by default)

### Step 2: Install Backend Components

```bash
# Navigate to your Pterodactyl directory
cd /var/www/pterodactyl

# Install/update Composer dependencies (if needed)
composer install --no-dev --optimize-autoloader

# Run database migrations
php artisan migrate

# Clear application cache
php artisan config:clear
php artisan cache:clear
php artisan view:clear
```

### Step 3: Install Frontend Components

```bash
# Install npm dependencies (if not already done)
npm install

# Compile frontend assets
npm run build:production
```

### Step 4: Set Permissions

```bash
# Set proper ownership (adjust user/group as needed)
chown -R www-data:www-data /var/www/pterodactyl

# Set proper permissions
chmod -R 755 /var/www/pterodactyl
```

## Usage

### For Administrators

1. **Access Admin Interface**
   - Navigate to Admin Panel → Daily Admin Notices
   - Click "Create New" to add a notice

2. **Creating a Notice**
   - **Title**: Short, descriptive title for the notice
   - **Content**: Full notice content (HTML supported)
   - **Type**: Choose visual style (Info, Success, Warning, Danger)
   - **Target Roles**: Select which user types see the notice (leave empty for all)
   - **Active**: Toggle notice visibility
   - **Dismissible**: Allow users to dismiss the notice
   - **Start/End Dates**: Optional scheduling

3. **Managing Notices**
   - View all notices in the admin interface
   - Edit existing notices
   - Track user dismissals
   - Delete outdated notices

### For Users

1. **Viewing Notices**
   - Active notices appear at the top of your dashboard
   - Different colors indicate notice importance
   - Click the X button to dismiss (if allowed)

2. **Notice Types**
   - **Blue (Info)**: General information
   - **Green (Success)**: Positive updates
   - **Yellow (Warning)**: Important warnings
   - **Red (Danger)**: Critical alerts

## API Documentation

### Client API Endpoints

#### Get Active Notices
```http
GET /api/client/daily-admin-notices
Authorization: Bearer {token}
```

#### Dismiss Notice
```http
POST /api/client/daily-admin-notices/{id}/dismiss
Authorization: Bearer {token}
```

### Admin API Endpoints

#### List All Notices
```http
GET /api/application/daily-admin-notices
Authorization: Bearer {token}
```

#### Get Single Notice
```http
GET /api/application/daily-admin-notices/{id}
Authorization: Bearer {token}
```

#### Create Notice
```http
POST /api/application/daily-admin-notices
Authorization: Bearer {token}
Content-Type: application/json

{
    "title": "Maintenance Notice",
    "content": "Scheduled maintenance tonight from 2-4 AM EST.",
    "type": "warning",
    "target_roles": ["user"],
    "is_active": true,
    "is_dismissible": true,
    "starts_at": "2024-01-15T02:00:00Z",
    "expires_at": "2024-01-15T04:00:00Z"
}
```

#### Update Notice
```http
PATCH /api/application/daily-admin-notices/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
    "title": "Updated Title",
    "is_active": false
}
```

#### Delete Notice
```http
DELETE /api/application/daily-admin-notices/{id}
Authorization: Bearer {token}
```

## Database Schema

### daily_admin_notices
- `id` - Primary key
- `title` - Notice title
- `content` - Notice content (HTML allowed)
- `type` - Notice type (info, success, warning, danger)
- `target_roles` - JSON array of target roles (null = all users)
- `is_active` - Whether notice is active
- `is_dismissible` - Whether users can dismiss
- `starts_at` - Optional start date/time
- `expires_at` - Optional end date/time
- `created_by` - User ID who created the notice
- `created_at` - Creation timestamp
- `updated_at` - Last update timestamp

### daily_admin_notice_dismissals
- `id` - Primary key
- `notice_id` - Foreign key to daily_admin_notices
- `user_id` - Foreign key to users
- `dismissed_at` - Dismissal timestamp

## Troubleshooting

### Common Issues

1. **Notices not appearing on dashboard**
   - Check if notices are active (`is_active = true`)
   - Verify start/end dates are correct
   - Ensure target roles include the user's role
   - Clear browser cache

2. **Admin interface not accessible**
   - Verify admin routes are properly registered
   - Check user has admin permissions
   - Clear application cache: `php artisan cache:clear`

3. **Database errors**
   - Ensure migrations have run: `php artisan migrate:status`
   - Check database permissions
   - Verify foreign key constraints

4. **Frontend not updating**
   - Rebuild assets: `npm run build:production`
   - Clear browser cache
   - Check browser console for JavaScript errors

### Log Files
- Application logs: `storage/logs/laravel.log`
- Web server logs: `/var/log/nginx/error.log` or `/var/log/apache2/error.log`

## Uninstallation

To remove the addon:

```bash
# Remove database tables
php artisan migrate:rollback --step=2

# Remove files (manual cleanup required)
# - Remove admin blade templates: resources/views/admin/daily-admin-notices/
# - Remove React components: resources/scripts/components/dashboard/DailyAdminNoticeBar.tsx
# - Remove API files: resources/scripts/api/dailyAdminNotices.ts
# - Remove backend files: app/Models/DailyAdminNotice*.php, app/Services/DailyAdminNotices/, etc.

# Rebuild frontend
npm run build:production

# Clear caches
php artisan config:clear
php artisan cache:clear
php artisan view:clear
```

## Support

For support, bug reports, or feature requests:
- Create an issue on the marketplace page
- Contact the developer through the marketplace messaging system

## License

This addon is licensed for use with Pterodactyl Panel installations. Redistribution or modification requires permission from the original author.

## Changelog

### Version 1.0.0
- Initial release
- Dashboard notice bar component
- Admin management interface
- Full API support
- Role-based targeting
- Dismissal tracking
- Scheduled notices
