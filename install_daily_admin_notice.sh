#!/bin/bash

# Daily Admin Notice Addon Installation Script
# For Pterodactyl Panel

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root. Please run as the web server user (usually www-data)."
   exit 1
fi

# Get the current directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PTERODACTYL_DIR="$(pwd)"

print_status "Daily Admin Notice Addon Installation"
print_status "======================================"
print_status "Script directory: $SCRIPT_DIR"
print_status "Pterodactyl directory: $PTERODACTYL_DIR"
echo

# Verify we're in a Pterodactyl directory
if [[ ! -f "artisan" ]] || [[ ! -f "composer.json" ]]; then
    print_error "This doesn't appear to be a Pterodactyl Panel directory."
    print_error "Please run this script from your Pterodactyl Panel root directory."
    exit 1
fi

print_success "Pterodactyl Panel directory confirmed."

# Check if PHP is available
if ! command -v php &> /dev/null; then
    print_error "PHP is not installed or not in PATH."
    exit 1
fi

# Check PHP version
PHP_VERSION=$(php -r "echo PHP_VERSION;")
print_status "PHP version: $PHP_VERSION"

# Check if composer is available
if ! command -v composer &> /dev/null; then
    print_error "Composer is not installed or not in PATH."
    exit 1
fi

# Check if npm is available
if ! command -v npm &> /dev/null; then
    print_warning "npm is not installed or not in PATH. Frontend compilation will be skipped."
    SKIP_FRONTEND=true
else
    SKIP_FRONTEND=false
fi

echo
print_status "Starting installation..."

# Step 1: Run database migrations
print_status "Step 1: Running database migrations..."
if php artisan migrate --force; then
    print_success "Database migrations completed successfully."
else
    print_error "Database migrations failed. Please check your database configuration."
    exit 1
fi

# Step 2: Clear application caches
print_status "Step 2: Clearing application caches..."
php artisan config:clear
php artisan cache:clear
php artisan view:clear
print_success "Application caches cleared."

# Step 3: Install/update composer dependencies (if needed)
print_status "Step 3: Checking Composer dependencies..."
if composer install --no-dev --optimize-autoloader --no-interaction; then
    print_success "Composer dependencies updated."
else
    print_warning "Composer install had issues, but continuing..."
fi

# Step 4: Frontend compilation (if npm is available)
if [[ "$SKIP_FRONTEND" == false ]]; then
    print_status "Step 4: Installing npm dependencies and compiling frontend assets..."
    
    if npm install; then
        print_success "npm dependencies installed."
    else
        print_warning "npm install had issues, but continuing..."
    fi
    
    if npm run build:production; then
        print_success "Frontend assets compiled successfully."
    else
        print_warning "Frontend compilation had issues. You may need to run 'npm run build:production' manually."
    fi
else
    print_warning "Step 4: Skipping frontend compilation (npm not available)."
    print_warning "You will need to run 'npm install && npm run build:production' manually."
fi

# Step 5: Verify installation
print_status "Step 5: Verifying installation..."

# Check if migrations ran
if php artisan migrate:status | grep -q "daily_admin_notices"; then
    print_success "Database tables created successfully."
else
    print_error "Database tables were not created properly."
    exit 1
fi

# Check if models can be loaded
if php artisan tinker --execute="use Pterodactyl\Models\DailyAdminNotice; echo 'Model loaded successfully';" 2>/dev/null | grep -q "Model loaded successfully"; then
    print_success "Models are loading correctly."
else
    print_warning "Models may not be loading correctly. Check for any PHP errors."
fi

echo
print_success "=============================================="
print_success "Daily Admin Notice Addon Installation Complete!"
print_success "=============================================="
echo
print_status "Next steps:"
print_status "1. Access your Pterodactyl admin panel"
print_status "2. Navigate to 'Daily Admin Notices' in the admin menu"
print_status "3. Create your first notice to test the functionality"
echo
print_status "For support and documentation, see DAILY_ADMIN_NOTICE_README.md"

# Optional: Create a test notice
echo
read -p "Would you like to create a test notice? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Creating test notice..."
    
    # Get first admin user
    ADMIN_ID=$(php artisan tinker --execute="echo Pterodactyl\Models\User::where('root_admin', true)->first()->id ?? 1;" 2>/dev/null | tail -n 1)
    
    if php artisan tinker --execute="
        use Pterodactyl\Models\DailyAdminNotice;
        \$notice = DailyAdminNotice::create([
            'title' => 'Welcome to Daily Admin Notices!',
            'content' => 'This is a test notice to verify the addon is working correctly. You can edit or delete this notice from the admin panel.',
            'type' => 'success',
            'is_active' => true,
            'is_dismissible' => true,
            'created_by' => $ADMIN_ID
        ]);
        echo 'Test notice created with ID: ' . \$notice->id;
    " 2>/dev/null; then
        print_success "Test notice created! Check your dashboard to see it in action."
    else
        print_warning "Could not create test notice. You can create one manually from the admin panel."
    fi
fi

echo
print_success "Installation completed successfully!"
print_status "Thank you for using Daily Admin Notice addon!"
