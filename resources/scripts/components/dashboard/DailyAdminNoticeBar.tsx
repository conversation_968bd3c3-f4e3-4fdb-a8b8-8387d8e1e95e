import React, { useEffect, useState } from 'react';
import { XIcon } from '@heroicons/react/outline';
import { ExclamationIcon, InformationCircleIcon, CheckCircleIcon, ShieldExclamationIcon } from '@heroicons/react/solid';
import tw from 'twin.macro';
import styled from 'styled-components/macro';
import { getDailyAdminNotices, dismissDailyAdminNotice } from '@/api/dailyAdminNotices';
import useFlash from '@/plugins/useFlash';

interface DailyAdminNotice {
    id: number;
    title: string;
    content: string;
    type: 'info' | 'warning' | 'success' | 'danger';
    is_dismissible: boolean;
    starts_at: string | null;
    expires_at: string | null;
    created_at: string;
}

const NoticeContainer = styled.div<{ $type: string }>`
    ${tw`flex items-start p-4 mb-4 rounded-lg border-l-4`};
    
    ${props => {
        switch (props.$type) {
            case 'danger':
                return tw`bg-red-50 border-red-400 text-red-800`;
            case 'warning':
                return tw`bg-yellow-50 border-yellow-400 text-yellow-800`;
            case 'success':
                return tw`bg-green-50 border-green-400 text-green-800`;
            case 'info':
            default:
                return tw`bg-blue-50 border-blue-400 text-blue-800`;
        }
    }}
`;

const getIcon = (type: string) => {
    const iconProps = { className: 'w-5 h-5 mr-3 mt-0.5 flex-shrink-0' };
    
    switch (type) {
        case 'danger':
            return <ShieldExclamationIcon {...iconProps} />;
        case 'warning':
            return <ExclamationIcon {...iconProps} />;
        case 'success':
            return <CheckCircleIcon {...iconProps} />;
        case 'info':
        default:
            return <InformationCircleIcon {...iconProps} />;
    }
};

export default function DailyAdminNoticeBar() {
    const [notices, setNotices] = useState<DailyAdminNotice[]>([]);
    const [loading, setLoading] = useState(true);
    const { clearAndAddHttpError } = useFlash();

    useEffect(() => {
        getDailyAdminNotices()
            .then(data => {
                setNotices(data.data || []);
            })
            .catch(error => {
                console.error('Failed to fetch daily admin notices:', error);
                clearAndAddHttpError({ key: 'daily-notices', error });
            })
            .finally(() => {
                setLoading(false);
            });
    }, []);

    const handleDismiss = async (noticeId: number) => {
        try {
            await dismissDailyAdminNotice(noticeId);
            setNotices(prev => prev.filter(notice => notice.id !== noticeId));
        } catch (error) {
            console.error('Failed to dismiss notice:', error);
            clearAndAddHttpError({ key: 'daily-notices', error });
        }
    };

    if (loading || notices.length === 0) {
        return null;
    }

    return (
        <div css={tw`space-y-4 mb-6`}>
            {notices.map(notice => (
                <NoticeContainer key={notice.id} $type={notice.type}>
                    {getIcon(notice.type)}
                    <div css={tw`flex-1 min-w-0`}>
                        <h3 css={tw`text-sm font-medium mb-1`}>
                            {notice.title}
                        </h3>
                        <div
                            css={tw`text-sm`}
                            dangerouslySetInnerHTML={{ __html: notice.content }}
                        />
                    </div>
                    {notice.is_dismissible && (
                        <button
                            onClick={() => handleDismiss(notice.id)}
                            css={tw`ml-4 flex-shrink-0 p-1 rounded-md hover:bg-black hover:bg-opacity-10 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent focus:ring-white`}
                            aria-label="Dismiss notice"
                        >
                            <XIcon css={tw`w-4 h-4`} />
                        </button>
                    )}
                </NoticeContainer>
            ))}
        </div>
    );
}
