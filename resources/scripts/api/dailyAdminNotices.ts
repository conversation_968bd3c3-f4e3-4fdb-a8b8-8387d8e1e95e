import http from '@/api/http';

export interface DailyAdminNotice {
    id: number;
    title: string;
    content: string;
    type: 'info' | 'warning' | 'success' | 'danger';
    target_roles: string[] | null;
    is_active: boolean;
    is_dismissible: boolean;
    starts_at: string | null;
    expires_at: string | null;
    created_by: number;
    created_at: string;
    updated_at: string;
}

export interface DailyAdminNoticeResponse {
    object: string;
    data: DailyAdminNotice[];
}

export interface CreateDailyAdminNoticeRequest {
    title: string;
    content: string;
    type: 'info' | 'warning' | 'success' | 'danger';
    target_roles?: string[] | null;
    is_active?: boolean;
    is_dismissible?: boolean;
    starts_at?: string | null;
    expires_at?: string | null;
}

export interface UpdateDailyAdminNoticeRequest extends Partial<CreateDailyAdminNoticeRequest> {}

// Client API functions (for regular users)
export const getDailyAdminNotices = (): Promise<DailyAdminNoticeResponse> => {
    return new Promise((resolve, reject) => {
        console.log('API: Making request to /api/client/daily-admin-notices');
        console.log('API: Using http client:', http);

        http.get('/api/client/daily-admin-notices')
            .then((response) => {
                console.log('API: Full response:', response);
                console.log('API: Response data:', response.data);
                resolve(response.data);
            })
            .catch(error => {
                console.error('API: Request failed with error:', error);
                console.error('API: Error response:', error.response);
                console.error('API: Error status:', error.response?.status);
                console.error('API: Error data:', error.response?.data);
                reject(error);
            });
    });
};

export const dismissDailyAdminNotice = (noticeId: number): Promise<void> => {
    return new Promise((resolve, reject) => {
        http.post(`/api/client/daily-admin-notices/${noticeId}/dismiss`)
            .then(() => resolve())
            .catch(reject);
    });
};

// Admin API functions (for administrators)
export const getAdminDailyAdminNotices = (page = 1): Promise<DailyAdminNoticeResponse> => {
    return new Promise((resolve, reject) => {
        http.get('/api/application/daily-admin-notices', {
            params: { page, include: ['creator'] }
        })
            .then(({ data }) => resolve(data))
            .catch(reject);
    });
};

export const getAdminDailyAdminNotice = (noticeId: number): Promise<{ data: DailyAdminNotice }> => {
    return new Promise((resolve, reject) => {
        http.get(`/api/application/daily-admin-notices/${noticeId}`, {
            params: { include: ['creator'] }
        })
            .then(({ data }) => resolve(data))
            .catch(reject);
    });
};

export const createDailyAdminNotice = (data: CreateDailyAdminNoticeRequest): Promise<{ data: DailyAdminNotice }> => {
    return new Promise((resolve, reject) => {
        http.post('/api/application/daily-admin-notices', data)
            .then(({ data }) => resolve(data))
            .catch(reject);
    });
};

export const updateDailyAdminNotice = (noticeId: number, data: UpdateDailyAdminNoticeRequest): Promise<{ data: DailyAdminNotice }> => {
    return new Promise((resolve, reject) => {
        http.patch(`/api/application/daily-admin-notices/${noticeId}`, data)
            .then(({ data }) => resolve(data))
            .catch(reject);
    });
};

export const deleteDailyAdminNotice = (noticeId: number): Promise<void> => {
    return new Promise((resolve, reject) => {
        http.delete(`/api/application/daily-admin-notices/${noticeId}`)
            .then(() => resolve())
            .catch(reject);
    });
};
