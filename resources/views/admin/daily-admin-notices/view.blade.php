@extends('layouts.admin')

@section('title')
    Daily Admin Notice: {{ $notice->title }}
@endsection

@section('content-header')
    <h1>{{ $notice->title }}<small>{{ $notice->id }}</small></h1>
    <ol class="breadcrumb">
        <li><a href="{{ route('admin.index') }}">Admin</a></li>
        <li><a href="{{ route('admin.daily-admin-notices') }}">Daily Admin Notices</a></li>
        <li class="active">{{ $notice->title }}</li>
    </ol>
@endsection

@section('content')
<div class="row">
    <div class="col-xs-12">
        <div class="nav-tabs-custom nav-tabs-floating">
            <ul class="nav nav-tabs">
                <li class="active"><a href="#details" data-toggle="tab">Details</a></li>
                <li><a href="#dismissals" data-toggle="tab">Dismissals</a></li>
            </ul>
            <div class="tab-content">
                <div class="tab-pane active" id="details">
                    <form action="{{ route('admin.daily-admin-notices.view', $notice->id) }}" method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="title" class="form-label">Title</label>
                                    <input type="text" id="title" name="title" class="form-control" value="{{ old('title', $notice->title) }}" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="type" class="form-label">Type</label>
                                    <select id="type" name="type" class="form-control" required>
                                        <option value="info" {{ old('type', $notice->type) === 'info' ? 'selected' : '' }}>Info</option>
                                        <option value="success" {{ old('type', $notice->type) === 'success' ? 'selected' : '' }}>Success</option>
                                        <option value="warning" {{ old('type', $notice->type) === 'warning' ? 'selected' : '' }}>Warning</option>
                                        <option value="danger" {{ old('type', $notice->type) === 'danger' ? 'selected' : '' }}>Danger</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="target_roles" class="form-label">Target Roles</label>
                                    <select id="target_roles" name="target_roles[]" class="form-control" multiple>
                                        <option value="admin" {{ in_array('admin', old('target_roles', $notice->target_roles ?? [])) ? 'selected' : '' }}>Administrators</option>
                                        <option value="user" {{ in_array('user', old('target_roles', $notice->target_roles ?? [])) ? 'selected' : '' }}>Users</option>
                                    </select>
                                    <p class="text-muted small">Leave empty to show to all users.</p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="content" class="form-label">Content</label>
                                    <textarea id="content" name="content" class="form-control" rows="4" required>{{ old('content', $notice->content) }}</textarea>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="starts_at" class="form-label">Start Date/Time</label>
                                    <input type="datetime-local" id="starts_at" name="starts_at" class="form-control" 
                                           value="{{ old('starts_at', $notice->starts_at ? $notice->starts_at->format('Y-m-d\TH:i') : '') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="expires_at" class="form-label">End Date/Time</label>
                                    <input type="datetime-local" id="expires_at" name="expires_at" class="form-control" 
                                           value="{{ old('expires_at', $notice->expires_at ? $notice->expires_at->format('Y-m-d\TH:i') : '') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" name="is_active" value="1" {{ old('is_active', $notice->is_active) ? 'checked' : '' }}>
                                            Active
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" name="is_dismissible" value="1" {{ old('is_dismissible', $notice->is_dismissible) ? 'checked' : '' }}>
                                            Dismissible
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                {!! method_field('PATCH') !!}
                                {!! csrf_field() !!}
                                <button type="submit" class="btn btn-primary btn-sm">Update Notice</button>
                                <button type="button" class="btn btn-danger btn-sm pull-right" data-action="delete-notice">Delete Notice</button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="tab-pane" id="dismissals">
                    <div class="row">
                        <div class="col-md-12">
                            <h4>User Dismissals</h4>
                            @if($notice->dismissals->count() > 0)
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>User</th>
                                            <th>Dismissed At</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($notice->dismissals as $dismissal)
                                            <tr>
                                                <td>{{ $dismissal->user->username ?? 'Unknown User' }}</td>
                                                <td>{{ $dismissal->dismissed_at->format('M j, Y H:i:s') }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            @else
                                <p class="text-muted">No users have dismissed this notice yet.</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('footer-scripts')
    @parent
    <script>
        $('[data-action="delete-notice"]').click(function (event) {
            event.preventDefault();
            swal({
                type: 'error',
                title: 'Delete Notice',
                text: 'Are you sure you want to delete this notice? This action cannot be undone.',
                showCancelButton: true,
                allowOutsideClick: true,
                closeOnConfirm: false,
                confirmButtonText: 'Delete',
                confirmButtonColor: '#d9534f',
                showLoaderOnConfirm: true
            }, function () {
                $.ajax({
                    method: 'DELETE',
                    url: '{{ route('admin.daily-admin-notices.view', $notice->id) }}',
                    headers: { 'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content') }
                }).done(function () {
                    swal({
                        type: 'success',
                        title: '',
                        text: 'Notice has been deleted.'
                    });
                    window.location = '{{ route('admin.daily-admin-notices') }}';
                }).fail(function (jqXHR) {
                    console.error(jqXHR);
                    swal({
                        type: 'error',
                        title: 'Whoops!',
                        text: 'An error occurred while attempting to delete this notice.'
                    });
                });
            });
        });
    </script>
@endsection
