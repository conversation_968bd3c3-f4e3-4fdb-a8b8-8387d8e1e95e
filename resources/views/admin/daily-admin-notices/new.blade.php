@extends('layouts.admin')

@section('title')
    Create Daily Admin Notice
@endsection

@section('content-header')
    <h1>Create Daily Admin Notice<small>Add a new notice to be displayed to users.</small></h1>
    <ol class="breadcrumb">
        <li><a href="{{ route('admin.index') }}">Admin</a></li>
        <li><a href="{{ route('admin.daily-admin-notices') }}">Daily Admin Notices</a></li>
        <li class="active">Create</li>
    </ol>
@endsection

@section('content')
<div class="row">
    <div class="col-xs-12">
        <div class="box box-primary">
            <form action="{{ route('admin.daily-admin-notices') }}" method="POST">
                <div class="box-header with-border">
                    <h3 class="box-title">Notice Details</h3>
                </div>
                <div class="box-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="title" class="form-label">Title</label>
                                <input type="text" id="title" name="title" class="form-control" value="{{ old('title') }}" required>
                                <p class="text-muted small">The title of the notice that will be displayed to users.</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="type" class="form-label">Type</label>
                                <select id="type" name="type" class="form-control" required>
                                    <option value="info" {{ old('type') === 'info' ? 'selected' : '' }}>Info</option>
                                    <option value="success" {{ old('type') === 'success' ? 'selected' : '' }}>Success</option>
                                    <option value="warning" {{ old('type') === 'warning' ? 'selected' : '' }}>Warning</option>
                                    <option value="danger" {{ old('type') === 'danger' ? 'selected' : '' }}>Danger</option>
                                </select>
                                <p class="text-muted small">The visual style of the notice.</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="target_roles" class="form-label">Target Roles</label>
                                <select id="target_roles" name="target_roles[]" class="form-control" multiple>
                                    <option value="admin" {{ in_array('admin', old('target_roles', [])) ? 'selected' : '' }}>Administrators</option>
                                    <option value="user" {{ in_array('user', old('target_roles', [])) ? 'selected' : '' }}>Users</option>
                                </select>
                                <p class="text-muted small">Leave empty to show to all users. Hold Ctrl/Cmd to select multiple.</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="content" class="form-label">Content</label>
                                <textarea id="content" name="content" class="form-control" rows="4" required>{{ old('content') }}</textarea>
                                <p class="text-muted small">The content of the notice. HTML is allowed.</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="starts_at" class="form-label">Start Date/Time</label>
                                <input type="datetime-local" id="starts_at" name="starts_at" class="form-control" value="{{ old('starts_at') }}">
                                <p class="text-muted small">When the notice should start being displayed. Leave empty for immediate.</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="expires_at" class="form-label">End Date/Time</label>
                                <input type="datetime-local" id="expires_at" name="expires_at" class="form-control" value="{{ old('expires_at') }}">
                                <p class="text-muted small">When the notice should stop being displayed. Leave empty for no expiration.</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" name="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                        Active
                                    </label>
                                </div>
                                <p class="text-muted small">Whether the notice is currently active.</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" name="is_dismissible" value="1" {{ old('is_dismissible', true) ? 'checked' : '' }}>
                                        Dismissible
                                    </label>
                                </div>
                                <p class="text-muted small">Whether users can dismiss this notice.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="box-footer">
                    {!! csrf_field() !!}
                    <button type="submit" class="btn btn-success btn-sm">Create Notice</button>
                    <a href="{{ route('admin.daily-admin-notices') }}" class="btn btn-default btn-sm">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
