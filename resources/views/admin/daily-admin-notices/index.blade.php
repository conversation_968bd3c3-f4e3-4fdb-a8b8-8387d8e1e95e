@extends('layouts.admin')

@section('title')
    Daily Admin Notices
@endsection

@section('content-header')
    <h1>Daily Admin Notices<small>Manage notices displayed to users on their dashboard.</small></h1>
    <ol class="breadcrumb">
        <li><a href="{{ route('admin.index') }}">Admin</a></li>
        <li class="active">Daily Admin Notices</li>
    </ol>
@endsection

@section('content')
<div class="row">
    <div class="col-xs-12">
        <div class="box box-primary">
            <div class="box-header with-border">
                <h3 class="box-title">Notice List</h3>
                <div class="box-tools">
                    <a href="{{ route('admin.daily-admin-notices.new') }}" class="btn btn-sm btn-primary">Create New</a>
                </div>
            </div>
            <div class="box-body table-responsive no-padding">
                <table class="table table-hover">
                    <tbody>
                        <tr>
                            <th>ID</th>
                            <th>Title</th>
                            <th>Type</th>
                            <th>Target Roles</th>
                            <th>Status</th>
                            <th>Created By</th>
                            <th>Created At</th>
                            <th class="text-center">Actions</th>
                        </tr>
                        @forelse ($notices as $notice)
                            <tr>
                                <td><code>{{ $notice->id }}</code></td>
                                <td>
                                    <a href="{{ route('admin.daily-admin-notices.view', $notice->id) }}">
                                        {{ $notice->title }}
                                    </a>
                                </td>
                                <td>
                                    <span class="label label-{{ $notice->type === 'danger' ? 'danger' : ($notice->type === 'warning' ? 'warning' : ($notice->type === 'success' ? 'success' : 'info')) }}">
                                        {{ ucfirst($notice->type) }}
                                    </span>
                                </td>
                                <td>
                                    @if($notice->target_roles)
                                        {{ implode(', ', array_map('ucfirst', $notice->target_roles)) }}
                                    @else
                                        <em>All Users</em>
                                    @endif
                                </td>
                                <td>
                                    @if($notice->is_active)
                                        <span class="label label-success">Active</span>
                                    @else
                                        <span class="label label-default">Inactive</span>
                                    @endif
                                </td>
                                <td>{{ $notice->creator->username ?? 'Unknown' }}</td>
                                <td>{{ $notice->created_at->format('M j, Y H:i') }}</td>
                                <td class="text-center">
                                    <a href="{{ route('admin.daily-admin-notices.view', $notice->id) }}" class="btn btn-xs btn-primary">
                                        <i class="fa fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="text-center text-muted">
                                    No daily admin notices have been created.
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            @if($notices->hasPages())
                <div class="box-footer with-border">
                    <div class="col-md-12 text-center">{!! $notices->render() !!}</div>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
