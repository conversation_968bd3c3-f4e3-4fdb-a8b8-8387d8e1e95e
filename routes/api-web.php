<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

// This route will be accessible by authenticated panel users via session cookies.
Route::middleware(['web', 'auth'])->group(function () {
    // Console Copilot dedicated endpoint
    Route::post('/remote/console-copilot', [Pterodactyl\Http\Controllers\Api\Client\ConsoleCopilotController::class, 'analyze']);

    Route::post('/remote/gemini-proxy', function (Request $request) {
        // Validate the incoming request: ensure 'prompt' is provided
        $prompt = $request->input('prompt');
        if (empty($prompt)) {
            return response()->json(['message' => 'Prompt is required.'], 400);
        }

        // Retrieve the Gemini API key from environment variables (.env file)
        $apiKey = env('GEMINI_API_KEY');
        if (empty($apiKey)) {
            Log::error('GEMINI_API_KEY is not configured in the .env file.');
            return response()->json(['message' => 'Gemini API key is not configured on the server.'], 500);
        }

        try {
            // Make the HTTP request to the Gemini API
            $response = Http::timeout(60)
                           ->post("https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key={$apiKey}", [
                               'contents' => [
                                   [
                                       'parts' => [
                                           ['text' => $prompt]
                                       ]
                                   ]
                               ],
                               'safetySettings' => [
                                    ['category' => 'HARM_CATEGORY_HARASSMENT', 'threshold' => 'BLOCK_NONE'],
                                    ['category' => 'HARM_CATEGORY_HATE_SPEECH', 'threshold' => 'BLOCK_NONE'],
                                    ['category' => 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'threshold' => 'BLOCK_NONE'],
                                    ['category' => 'HARM_CATEGORY_DANGEROUS_CONTENT', 'threshold' => 'BLOCK_NONE'],
                               ],
                           ]);

            if (!$response->successful()) {
                $errorData = $response->json();
                Log::error('Gemini API Error Response:', ['status' => $response->status(), 'response' => $errorData, 'prompt_sent' => $prompt]);
                return response()->json(['message' => 'Error from Gemini API', 'details' => $errorData], $response->status());
            }

            $responseData = $response->json();
            $aiResponse = $responseData['candidates'][0]['content']['parts'][0]['text'] ?? 'No valid response from AI. This could be due to safety filters or an empty response.';

            return response()->json(['aiResponse' => $aiResponse]);

        } catch (\Exception $e) {
            Log::error('Proxy Server Error communicating with Gemini API: ' . $e->getMessage(), ['exception' => $e, 'prompt_attempted' => $prompt]);
            return response()->json(['message' => 'Internal server error: ' . $e->getMessage()], 500);
        }
    });
});