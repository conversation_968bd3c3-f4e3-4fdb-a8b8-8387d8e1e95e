<?php

namespace Pterodactyl\Repositories\Eloquent;

use Pterodactyl\Models\DailyAdminNotice;
use Pterodactyl\Models\DailyAdminNoticeDismissal;
use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Pterodactyl\Exceptions\Repository\RecordNotFoundException;
use Pterodactyl\Contracts\Repository\DailyAdminNoticeRepositoryInterface;

class DailyAdminNoticeRepository extends EloquentRepository implements DailyAdminNoticeRepositoryInterface
{
    /**
     * Return the model backing this repository.
     */
    public function model(): string
    {
        return DailyAdminNotice::class;
    }

    /**
     * Return all active notices that are currently valid and should be shown to a user.
     */
    public function getActiveNoticesForUser(int $userId, string $userRole): Collection
    {
        return $this->getBuilder()
            ->active()
            ->currentlyValid()
            ->forRole($userRole)
            ->notDismissedBy($userId)
            ->orderBy('created_at', 'desc')
            ->get($this->getColumns());
    }

    /**
     * Return all notices with their creator information.
     */
    public function getAllWithCreator(): Collection
    {
        return $this->getBuilder()
            ->with('creator:id,username,email')
            ->orderBy('created_at', 'desc')
            ->get($this->getColumns());
    }

    /**
     * Return a notice with its creator and dismissal information.
     *
     * @throws \Pterodactyl\Exceptions\Repository\RecordNotFoundException
     */
    public function getWithDetails(int $id): DailyAdminNotice
    {
        try {
            return $this->getBuilder()
                ->with(['creator:id,username,email', 'dismissals.user:id,username'])
                ->findOrFail($id, $this->getColumns());
        } catch (ModelNotFoundException) {
            throw new RecordNotFoundException();
        }
    }

    /**
     * Mark a notice as dismissed by a specific user.
     */
    public function dismissForUser(int $noticeId, int $userId): void
    {
        DailyAdminNoticeDismissal::firstOrCreate([
            'notice_id' => $noticeId,
            'user_id' => $userId,
        ]);
    }

    /**
     * Check if a notice has been dismissed by a specific user.
     */
    public function isDismissedByUser(int $noticeId, int $userId): bool
    {
        return DailyAdminNoticeDismissal::where('notice_id', $noticeId)
            ->where('user_id', $userId)
            ->exists();
    }
}
