<?php

namespace Pterodactyl\Services\DailyAdminNotices;

use Pterodactyl\Models\DailyAdminNotice;
use Pterodactyl\Contracts\Repository\DailyAdminNoticeRepositoryInterface;

class DailyAdminNoticeDeletionService
{
    /**
     * DailyAdminNoticeDeletionService constructor.
     */
    public function __construct(protected DailyAdminNoticeRepositoryInterface $repository)
    {
    }

    /**
     * Delete an existing daily admin notice.
     *
     * @throws \Pterodactyl\Exceptions\Repository\RecordNotFoundException
     */
    public function handle(DailyAdminNotice|int $notice): ?int
    {
        $notice = ($notice instanceof DailyAdminNotice) ? $notice->id : $notice;

        return $this->repository->delete($notice);
    }
}
