<?php

namespace Pterodactyl\Services\DailyAdminNotices;

use Pterodactyl\Models\DailyAdminNotice;
use Pterodactyl\Contracts\Repository\DailyAdminNoticeRepositoryInterface;

class DailyAdminNoticeUpdateService
{
    /**
     * DailyAdminNoticeUpdateService constructor.
     */
    public function __construct(protected DailyAdminNoticeRepositoryInterface $repository)
    {
    }

    /**
     * Update an existing daily admin notice.
     *
     * @throws \Pterodactyl\Exceptions\Model\DataValidationException
     * @throws \Pterodactyl\Exceptions\Repository\RecordNotFoundException
     */
    public function handle(DailyAdminNotice|int $notice, array $data): DailyAdminNotice
    {
        $notice = ($notice instanceof DailyAdminNotice) ? $notice->id : $notice;

        return $this->repository->update($notice, $data);
    }
}
