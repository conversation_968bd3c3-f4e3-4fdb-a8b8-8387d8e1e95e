<?php

namespace Pterodactyl\Services\DailyAdminNotices;

use Pterodactyl\Models\DailyAdminNotice;
use Pterodactyl\Contracts\Repository\DailyAdminNoticeRepositoryInterface;

class DailyAdminNoticeCreationService
{
    /**
     * DailyAdminNoticeCreationService constructor.
     */
    public function __construct(protected DailyAdminNoticeRepositoryInterface $repository)
    {
    }

    /**
     * Create a new daily admin notice.
     *
     * @throws \Pterodactyl\Exceptions\Model\DataValidationException
     */
    public function handle(array $data): DailyAdminNotice
    {
        return $this->repository->create($data);
    }
}
