<?php

namespace Pterodactyl\Services\DailyAdminNotices;

use Pterodactyl\Models\User;
use Illuminate\Support\Collection;
use Pterodactyl\Contracts\Repository\DailyAdminNoticeRepositoryInterface;

class DailyAdminNoticeRetrievalService
{
    /**
     * DailyAdminNoticeRetrievalService constructor.
     */
    public function __construct(protected DailyAdminNoticeRepositoryInterface $repository)
    {
    }

    /**
     * Get all active notices that should be displayed to a specific user.
     */
    public function getNoticesForUser(User $user): Collection
    {
        $role = $user->root_admin ? 'admin' : 'user';
        
        return $this->repository->getActiveNoticesForUser($user->id, $role);
    }

    /**
     * Get all notices for admin management interface.
     */
    public function getAllNoticesForAdmin(): Collection
    {
        return $this->repository->getAllWithCreator();
    }

    /**
     * Dismiss a notice for a specific user.
     */
    public function dismissNoticeForUser(int $noticeId, User $user): void
    {
        $this->repository->dismissForUser($noticeId, $user->id);
    }

    /**
     * Check if a notice has been dismissed by a user.
     */
    public function isNoticeDismissedByUser(int $noticeId, User $user): bool
    {
        return $this->repository->isDismissedByUser($noticeId, $user->id);
    }
}
