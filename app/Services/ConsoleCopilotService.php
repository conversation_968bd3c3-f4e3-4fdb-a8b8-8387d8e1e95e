<?php

namespace Pterodactyl\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ConsoleCopilotService
{
    /**
     * Advanced error patterns with context and solutions
     */
    private const ADVANCED_ERROR_PATTERNS = [
        'java' => [
            'memory_issues' => [
                'patterns' => [
                    '/OutOfMemoryError.*Java heap space/i',
                    '/GC overhead limit exceeded/i',
                    '/unable to create new native thread/i'
                ],
                'severity' => 'critical',
                'category' => 'memory',
                'quick_fix' => 'Increase JVM heap size with -Xmx parameter'
            ],
            'connection_issues' => [
                'patterns' => [
                    '/Connection refused/i',
                    '/Connection timed out/i',
                    '/Connection reset by peer/i'
                ],
                'severity' => 'error',
                'category' => 'network',
                'quick_fix' => 'Check network connectivity and firewall settings'
            ],
            'plugin_issues' => [
                'patterns' => [
                    '/Could not load plugin/i',
                    '/Plugin.*disabled/i',
                    '/Error occurred while enabling/i'
                ],
                'severity' => 'warning',
                'category' => 'plugin',
                'quick_fix' => 'Check plugin compatibility and dependencies'
            ]
        ],
        'nodejs' => [
            'module_issues' => [
                'patterns' => [
                    '/Cannot find module/i',
                    '/Module not found/i',
                    '/Error: Cannot resolve module/i'
                ],
                'severity' => 'error',
                'category' => 'dependency',
                'quick_fix' => 'Run npm install to install missing dependencies'
            ],
            'memory_issues' => [
                'patterns' => [
                    '/JavaScript heap out of memory/i',
                    '/FATAL ERROR.*Ineffective mark-compacts/i'
                ],
                'severity' => 'critical',
                'category' => 'memory',
                'quick_fix' => 'Increase Node.js memory limit with --max-old-space-size'
            ],
            'syntax_issues' => [
                'patterns' => [
                    '/SyntaxError/i',
                    '/Unexpected token/i',
                    '/ReferenceError/i'
                ],
                'severity' => 'error',
                'category' => 'code',
                'quick_fix' => 'Check code syntax and variable declarations'
            ]
        ],
        'python' => [
            'import_issues' => [
                'patterns' => [
                    '/ImportError/i',
                    '/ModuleNotFoundError/i',
                    '/No module named/i'
                ],
                'severity' => 'error',
                'category' => 'dependency',
                'quick_fix' => 'Install missing Python packages with pip'
            ],
            'syntax_issues' => [
                'patterns' => [
                    '/SyntaxError/i',
                    '/IndentationError/i',
                    '/TabError/i'
                ],
                'severity' => 'error',
                'category' => 'code',
                'quick_fix' => 'Check Python syntax and indentation'
            ]
        ]
    ];

    /**
     * Analyze console output and detect issues with advanced pattern matching
     */
    public function analyzeConsoleOutput(array $consoleLines, string $serverType = 'generic'): array
    {
        $detectedIssues = [];
        $patterns = self::ADVANCED_ERROR_PATTERNS[$serverType] ?? [];

        foreach ($consoleLines as $lineIndex => $line) {
            $cleanLine = trim($line);
            if (empty($cleanLine)) continue;

            foreach ($patterns as $issueType => $config) {
                foreach ($config['patterns'] as $pattern) {
                    if (preg_match($pattern, $cleanLine)) {
                        $detectedIssues[] = [
                            'line' => $cleanLine,
                            'line_number' => $lineIndex + 1,
                            'type' => $issueType,
                            'severity' => $config['severity'],
                            'category' => $config['category'],
                            'quick_fix' => $config['quick_fix'],
                            'timestamp' => now()->toISOString()
                        ];
                        break 2; // Break both loops to avoid duplicate detection
                    }
                }
            }
        }

        return $detectedIssues;
    }

    /**
     * Generate health score based on detected issues
     */
    public function calculateHealthScore(array $detectedIssues): array
    {
        if (empty($detectedIssues)) {
            return [
                'score' => 100,
                'status' => 'healthy',
                'message' => 'No issues detected in console output'
            ];
        }

        $criticalCount = 0;
        $errorCount = 0;
        $warningCount = 0;

        foreach ($detectedIssues as $issue) {
            switch ($issue['severity']) {
                case 'critical':
                    $criticalCount++;
                    break;
                case 'error':
                    $errorCount++;
                    break;
                case 'warning':
                    $warningCount++;
                    break;
            }
        }

        // Calculate score (100 = perfect, 0 = critical issues)
        $score = 100;
        $score -= ($criticalCount * 40); // Critical issues heavily impact score
        $score -= ($errorCount * 20);    // Errors moderately impact score
        $score -= ($warningCount * 5);   // Warnings lightly impact score
        $score = max(0, $score); // Ensure score doesn't go below 0

        // Determine status
        if ($criticalCount > 0) {
            $status = 'critical';
            $message = "Critical issues detected: {$criticalCount} critical, {$errorCount} errors, {$warningCount} warnings";
        } elseif ($errorCount > 0) {
            $status = 'error';
            $message = "Errors detected: {$errorCount} errors, {$warningCount} warnings";
        } elseif ($warningCount > 0) {
            $status = 'warning';
            $message = "Warnings detected: {$warningCount} warnings";
        } else {
            $status = 'healthy';
            $message = 'Console output appears healthy';
        }

        return [
            'score' => $score,
            'status' => $status,
            'message' => $message,
            'breakdown' => [
                'critical' => $criticalCount,
                'error' => $errorCount,
                'warning' => $warningCount,
                'total' => count($detectedIssues)
            ]
        ];
    }

    /**
     * Get server type specific recommendations
     */
    public function getServerTypeRecommendations(string $serverType, array $detectedIssues): array
    {
        $recommendations = [];

        switch ($serverType) {
            case 'java':
                $recommendations = [
                    'memory' => 'Consider increasing JVM heap size (-Xmx) and enabling G1GC for better memory management',
                    'performance' => 'Monitor server TPS and consider optimizing plugins or world generation settings',
                    'plugins' => 'Regularly update plugins and remove unused ones to prevent conflicts'
                ];
                break;

            case 'nodejs':
                $recommendations = [
                    'memory' => 'Increase Node.js memory limit and monitor for memory leaks',
                    'dependencies' => 'Keep npm packages updated and use npm audit to check for vulnerabilities',
                    'performance' => 'Use PM2 or similar process manager for better stability'
                ];
                break;

            case 'python':
                $recommendations = [
                    'dependencies' => 'Use virtual environments and keep requirements.txt updated',
                    'performance' => 'Consider using gunicorn or uwsgi for production deployments',
                    'monitoring' => 'Implement proper logging and error tracking'
                ];
                break;

            default:
                $recommendations = [
                    'monitoring' => 'Implement comprehensive logging and monitoring',
                    'resources' => 'Monitor CPU, memory, and disk usage regularly',
                    'updates' => 'Keep server software and dependencies updated'
                ];
        }

        return $recommendations;
    }

    /**
     * Cache analysis results to avoid repeated API calls
     */
    public function cacheAnalysis(string $cacheKey, array $analysisData, int $ttlMinutes = 10): void
    {
        Cache::put($cacheKey, $analysisData, now()->addMinutes($ttlMinutes));
    }

    /**
     * Get cached analysis if available
     */
    public function getCachedAnalysis(string $cacheKey): ?array
    {
        return Cache::get($cacheKey);
    }

    /**
     * Generate cache key for console analysis
     */
    public function generateCacheKey(array $consoleOutput, array $detectedIssues): string
    {
        $content = json_encode([
            'output' => array_slice($consoleOutput, -50), // Last 50 lines
            'issues' => $detectedIssues,
            'timestamp' => now()->format('Y-m-d-H') // Cache per hour
        ]);

        return 'console_copilot:' . md5($content);
    }

    /**
     * Log analysis for debugging and improvement
     */
    public function logAnalysis(array $analysisData, bool $wasCached = false): void
    {
        Log::info('Console Copilot Analysis', [
            'cached' => $wasCached,
            'issues_count' => count($analysisData['detected_issues'] ?? []),
            'health_score' => $analysisData['health_score']['score'] ?? null,
            'server_type' => $analysisData['server_type'] ?? 'unknown'
        ]);
    }
}
