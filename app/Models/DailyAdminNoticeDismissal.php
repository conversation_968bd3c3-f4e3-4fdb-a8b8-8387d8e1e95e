<?php

namespace Pterodactyl\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $notice_id
 * @property int $user_id
 * @property \Carbon\Carbon $dismissed_at
 * @property \Pterodactyl\Models\DailyAdminNotice $notice
 * @property \Pterodactyl\Models\User $user
 */
class DailyAdminNoticeDismissal extends Model
{
    /**
     * The resource name for this model when it is transformed into an
     * API representation using fractal.
     */
    public const RESOURCE_NAME = 'daily_admin_notice_dismissal';

    /**
     * The table associated with the model.
     */
    protected $table = 'daily_admin_notice_dismissals';

    /**
     * Indicates if the model should be timestamped.
     */
    public $timestamps = false;

    /**
     * Fields that are not mass assignable.
     */
    protected $guarded = ['id'];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'dismissed_at' => 'datetime',
    ];

    /**
     * Rules ensuring that the raw data stored in the database meets expectations.
     */
    public static array $validationRules = [
        'notice_id' => 'required|integer|exists:daily_admin_notices,id',
        'user_id' => 'required|integer|exists:users,id',
    ];

    /**
     * Gets the notice that was dismissed.
     */
    public function notice(): BelongsTo
    {
        return $this->belongsTo(DailyAdminNotice::class, 'notice_id');
    }

    /**
     * Gets the user who dismissed the notice.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
