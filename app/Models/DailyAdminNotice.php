<?php

namespace Pterodactyl\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;

/**
 * @property int $id
 * @property string $title
 * @property string $content
 * @property string $type
 * @property array|null $target_roles
 * @property bool $is_active
 * @property bool $is_dismissible
 * @property \Carbon\Carbon|null $starts_at
 * @property \Carbon\Carbon|null $expires_at
 * @property int $created_by
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \Pterodactyl\Models\User $creator
 * @property \Pterodactyl\Models\DailyAdminNoticeDismissal[] $dismissals
 */
class DailyAdminNotice extends Model
{
    /**
     * The resource name for this model when it is transformed into an
     * API representation using fractal.
     */
    public const RESOURCE_NAME = 'daily_admin_notice';

    /**
     * The table associated with the model.
     */
    protected $table = 'daily_admin_notices';

    /**
     * Fields that are not mass assignable.
     */
    protected $guarded = ['id', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'target_roles' => 'array',
        'is_active' => 'boolean',
        'is_dismissible' => 'boolean',
        'starts_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    /**
     * Rules ensuring that the raw data stored in the database meets expectations.
     */
    public static array $validationRules = [
        'title' => 'required|string|max:255',
        'content' => 'required|string|max:65535',
        'type' => 'required|string|in:info,warning,success,danger',
        'target_roles' => 'nullable|array',
        'target_roles.*' => 'string|in:admin,user',
        'is_active' => 'boolean',
        'is_dismissible' => 'boolean',
        'starts_at' => 'nullable|date',
        'expires_at' => 'nullable|date|after:starts_at',
        'created_by' => 'required|integer|exists:users,id',
    ];

    /**
     * {@inheritDoc}
     */
    public function getRouteKeyName(): string
    {
        return $this->getKeyName();
    }

    /**
     * Gets the user who created this notice.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Gets all dismissals for this notice.
     */
    public function dismissals(): HasMany
    {
        return $this->hasMany(DailyAdminNoticeDismissal::class, 'notice_id');
    }

    /**
     * Scope to get only active notices.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get notices that are currently valid (within time range).
     */
    public function scopeCurrentlyValid(Builder $query): Builder
    {
        $now = Carbon::now();
        
        return $query->where(function ($q) use ($now) {
            $q->whereNull('starts_at')->orWhere('starts_at', '<=', $now);
        })->where(function ($q) use ($now) {
            $q->whereNull('expires_at')->orWhere('expires_at', '>=', $now);
        });
    }

    /**
     * Scope to get notices for a specific user role.
     */
    public function scopeForRole(Builder $query, string $role): Builder
    {
        return $query->where(function ($q) use ($role) {
            $q->whereNull('target_roles')
              ->orWhereJsonContains('target_roles', $role);
        });
    }

    /**
     * Scope to get notices not dismissed by a specific user.
     */
    public function scopeNotDismissedBy(Builder $query, int $userId): Builder
    {
        return $query->whereDoesntHave('dismissals', function ($q) use ($userId) {
            $q->where('user_id', $userId);
        });
    }

    /**
     * Check if this notice is currently valid (active and within time range).
     */
    public function isCurrentlyValid(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        $now = Carbon::now();

        if ($this->starts_at && $this->starts_at->isAfter($now)) {
            return false;
        }

        if ($this->expires_at && $this->expires_at->isBefore($now)) {
            return false;
        }

        return true;
    }

    /**
     * Check if this notice should be shown to a user with the given role.
     */
    public function shouldShowToRole(string $role): bool
    {
        if (!$this->target_roles) {
            return true; // Show to all roles if no specific targeting
        }

        return in_array($role, $this->target_roles);
    }
}
