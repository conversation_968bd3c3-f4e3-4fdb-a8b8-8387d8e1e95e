<?php

namespace Pterodactyl\Http\Requests\Api\Application\DailyAdminNotices;

use Pterodactyl\Models\DailyAdminNotice;
use Pterodactyl\Services\Acl\Api\AdminAcl;
use Pterodactyl\Http\Requests\Api\Application\ApplicationApiRequest;

class UpdateDailyAdminNoticeRequest extends ApplicationApiRequest
{
    protected ?string $resource = AdminAcl::RESOURCE_SETTINGS;

    protected int $permission = AdminAcl::WRITE;

    /**
     * Validation rules for updating a daily admin notice.
     */
    public function rules(): array
    {
        $notice = $this->route()->parameter('notice');
        
        return collect(DailyAdminNotice::getRulesForUpdate($notice->id))
            ->except(['created_by'])
            ->toArray();
    }

    /**
     * Return only the fields that we are interested in from the request.
     */
    public function validated($key = null, $default = null): array
    {
        $data = parent::validated();

        // Convert empty target_roles to null
        if (isset($data['target_roles']) && empty($data['target_roles'])) {
            $data['target_roles'] = null;
        }

        return $data;
    }
}
