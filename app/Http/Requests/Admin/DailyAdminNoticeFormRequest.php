<?php

namespace Pterodactyl\Http\Requests\Admin;

use Pterodactyl\Models\DailyAdminNotice;

class DailyAdminNoticeFormRequest extends AdminFormRequest
{
    /**
     * Set up the validation rules to use for these requests.
     */
    public function rules(): array
    {
        if ($this->method() === 'PATCH') {
            $rules = DailyAdminNotice::getRulesForUpdate($this->route()->parameter('notice')->id);
            // Remove created_by from update rules as it shouldn't be changed
            unset($rules['created_by']);
            return $rules;
        }

        // For POST requests, exclude created_by from validation as it's added automatically
        $rules = DailyAdminNotice::getRules();
        unset($rules['created_by']);
        return $rules;
    }

    /**
     * Return only the fields that we are interested in from the request.
     * This will include empty fields as a null value.
     */
    public function normalize(array $only = null): array
    {
        $data = parent::normalize($only);
        
        // Add the current user as the creator for new notices
        if ($this->method() === 'POST') {
            $data['created_by'] = $this->user()->id;
        }

        // Convert empty target_roles to null
        if (isset($data['target_roles']) && empty($data['target_roles'])) {
            $data['target_roles'] = null;
        }

        return $data;
    }
}
