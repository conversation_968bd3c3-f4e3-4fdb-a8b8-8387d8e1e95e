<?php

namespace Pterodactyl\Http\Controllers\Admin;

use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\Factory as ViewFactory;
use Pterodactyl\Models\DailyAdminNotice;
use Prologue\Alerts\AlertsMessageBag;
use Pterodactyl\Http\Controllers\Controller;
use Pterodactyl\Services\DailyAdminNotices\DailyAdminNoticeCreationService;
use Pterodactyl\Services\DailyAdminNotices\DailyAdminNoticeUpdateService;
use Pterodactyl\Services\DailyAdminNotices\DailyAdminNoticeDeletionService;
use Pterodactyl\Services\DailyAdminNotices\DailyAdminNoticeRetrievalService;
use Pterodactyl\Http\Requests\Admin\DailyAdminNoticeFormRequest;

class DailyAdminNoticeController extends Controller
{
    /**
     * DailyAdminNoticeController constructor.
     */
    public function __construct(
        protected AlertsMessageBag $alert,
        protected DailyAdminNoticeCreationService $creationService,
        protected DailyAdminNoticeDeletionService $deletionService,
        protected DailyAdminNoticeUpdateService $updateService,
        protected DailyAdminNoticeRetrievalService $retrievalService,
        protected ViewFactory $view
    ) {
    }

    /**
     * Render daily admin notice listing page.
     */
    public function index(): View
    {
        $notices = DailyAdminNotice::with('creator:id,username,email')
            ->orderBy('created_at', 'desc')
            ->paginate(25);

        return $this->view->make('admin.daily-admin-notices.index', [
            'notices' => $notices,
        ]);
    }

    /**
     * Render daily admin notice creation page.
     */
    public function create(): View
    {
        return $this->view->make('admin.daily-admin-notices.new');
    }

    /**
     * Handle the storage of a new daily admin notice.
     *
     * @throws \Pterodactyl\Exceptions\Model\DataValidationException
     */
    public function store(DailyAdminNoticeFormRequest $request): RedirectResponse
    {
        $notice = $this->creationService->handle($request->normalize());
        $this->alert->success(trans('admin/daily-admin-notices.notices.created', ['title' => htmlspecialchars($notice->title)]))->flash();

        return redirect()->route('admin.daily-admin-notices.view', $notice->id);
    }

    /**
     * Return details about a daily admin notice.
     *
     * @throws \Pterodactyl\Exceptions\Repository\RecordNotFoundException
     */
    public function view(int $notice): View
    {
        $notice = DailyAdminNotice::with(['creator:id,username,email', 'dismissals.user:id,username'])
            ->findOrFail($notice);

        return $this->view->make('admin.daily-admin-notices.view', [
            'notice' => $notice,
        ]);
    }

    /**
     * Handle updating a daily admin notice.
     *
     * @throws \Pterodactyl\Exceptions\Model\DataValidationException
     * @throws \Pterodactyl\Exceptions\Repository\RecordNotFoundException
     */
    public function update(DailyAdminNoticeFormRequest $request, DailyAdminNotice $notice): RedirectResponse
    {
        $notice = $this->updateService->handle($notice, $request->normalize());
        $this->alert->success(trans('admin/daily-admin-notices.notices.updated'))->flash();

        return redirect()->route('admin.daily-admin-notices.view', $notice->id);
    }

    /**
     * Handle deleting a daily admin notice.
     *
     * @throws \Pterodactyl\Exceptions\Repository\RecordNotFoundException
     */
    public function delete(DailyAdminNotice $notice): RedirectResponse
    {
        $this->deletionService->handle($notice);
        $this->alert->success(trans('admin/daily-admin-notices.notices.deleted'))->flash();

        return redirect()->route('admin.daily-admin-notices');
    }
}
