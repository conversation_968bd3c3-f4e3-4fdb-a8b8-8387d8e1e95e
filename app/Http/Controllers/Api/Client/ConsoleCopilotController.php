<?php

namespace Pterodactyl\Http\Controllers\Api\Client;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Pterodactyl\Http\Controllers\Api\Client\ClientApiController;
use Pterodactyl\Services\ConsoleCopilotService;

class ConsoleCopilotController extends ClientApiController
{
    public function __construct(private ConsoleCopilotService $copilotService)
    {
        parent::__construct();
    }


    /**
     * Analyze console output for errors and provide AI-powered solutions
     */
    public function analyze(Request $request): JsonResponse
    {
        $request->validate([
            'console_output' => 'required|array',
            'detected_issues' => 'array',
            'server_type' => 'string|nullable',
        ]);

        $consoleOutput = $request->input('console_output', []);
        $frontendIssues = $request->input('detected_issues', []);
        $serverType = $request->input('server_type', 'generic');

        // Use service for advanced issue detection
        $detectedIssues = $this->copilotService->analyzeConsoleOutput($consoleOutput, $serverType);

        // Merge with frontend-detected issues if any
        if (!empty($frontendIssues)) {
            foreach ($frontendIssues as $issue) {
                $detectedIssues[] = [
                    'line' => $issue,
                    'type' => 'frontend_detected',
                    'severity' => 'warning',
                    'category' => 'general'
                ];
            }
        }

        // Generate cache key
        $cacheKey = $this->copilotService->generateCacheKey($consoleOutput, $detectedIssues);

        // Check for cached analysis
        $cachedAnalysis = $this->copilotService->getCachedAnalysis($cacheKey);
        if ($cachedAnalysis) {
            $this->copilotService->logAnalysis($cachedAnalysis, true);
            return response()->json($cachedAnalysis);
        }

        // Get Gemini API key
        $apiKey = env('GEMINI_API_KEY');
        if (empty($apiKey)) {
            Log::error('GEMINI_API_KEY is not configured in the .env file.');
            return response()->json(['message' => 'Gemini API key is not configured on the server.'], 500);
        }

        try {
            // Calculate health score
            $healthScore = $this->copilotService->calculateHealthScore($detectedIssues);

            // Get server-specific recommendations
            $recommendations = $this->copilotService->getServerTypeRecommendations($serverType, $detectedIssues);

            // Generate specialized prompt
            $prompt = $this->generateAnalysisPrompt($consoleOutput, $detectedIssues, $healthScore, $recommendations, $serverType);

            // Make request to Gemini API
            $response = Http::timeout(60)
                ->post("https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key={$apiKey}", [
                    'contents' => [
                        [
                            'parts' => [
                                ['text' => $prompt]
                            ]
                        ]
                    ],
                    'safetySettings' => [
                        ['category' => 'HARM_CATEGORY_HARASSMENT', 'threshold' => 'BLOCK_NONE'],
                        ['category' => 'HARM_CATEGORY_HATE_SPEECH', 'threshold' => 'BLOCK_NONE'],
                        ['category' => 'HARM_CATEGORY_SEXUALLY_EXPLICIT', 'threshold' => 'BLOCK_NONE'],
                        ['category' => 'HARM_CATEGORY_DANGEROUS_CONTENT', 'threshold' => 'BLOCK_NONE'],
                    ],
                    'generationConfig' => [
                        'temperature' => 0.3, // Lower temperature for more focused, technical responses
                        'topP' => 0.8,
                        'topK' => 40,
                        'maxOutputTokens' => 2048,
                    ]
                ]);

            if (!$response->successful()) {
                $errorData = $response->json();
                Log::error('Gemini API Error Response:', [
                    'status' => $response->status(), 
                    'response' => $errorData, 
                    'prompt_sent' => substr($prompt, 0, 500) . '...'
                ]);
                return response()->json(['message' => 'Error from Gemini API', 'details' => $errorData], $response->status());
            }

            $responseData = $response->json();
            $aiAnalysis = $responseData['candidates'][0]['content']['parts'][0]['text'] ?? 'No valid response from AI.';

            // Prepare comprehensive response
            $analysisResult = [
                'analysis' => $aiAnalysis,
                'cached' => false,
                'health_score' => $healthScore,
                'detected_issues' => $detectedIssues,
                'recommendations' => $recommendations,
                'server_type' => $serverType,
                'timestamp' => now()->toISOString()
            ];

            // Cache the analysis
            $this->copilotService->cacheAnalysis($cacheKey, $analysisResult, 10);

            // Log the analysis
            $this->copilotService->logAnalysis($analysisResult, false);

            return response()->json($analysisResult);

        } catch (\Exception $exception) {
            Log::error('Console Copilot Analysis Error:', [
                'error' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString()
            ]);
            
            return response()->json([
                'message' => 'Failed to analyze console output',
                'error' => $exception->getMessage()
            ], 500);
        }
    }



    /**
     * Generate specialized analysis prompt based on detected issues and health score
     */
    private function generateAnalysisPrompt(array $consoleOutput, array $detectedIssues, array $healthScore, array $recommendations, string $serverType): string
    {
        $recentLogs = implode("\n", array_slice($consoleOutput, -50));

        // Format detected issues for the prompt
        $issuesText = '';
        foreach (array_slice($detectedIssues, 0, 10) as $issue) {
            $issuesText .= "- [{$issue['severity']}] {$issue['line']}\n";
            if (isset($issue['quick_fix'])) {
                $issuesText .= "  Quick Fix: {$issue['quick_fix']}\n";
            }
        }

        $serverTypeContext = $this->getServerTypeContext($serverType);

        return "You are a Console Copilot AI for Pterodactyl game server management. Analyze the following console output and provide expert-level solutions.

SERVER TYPE: {$serverType}
{$serverTypeContext}

HEALTH SCORE: {$healthScore['score']}/100 ({$healthScore['status']})
{$healthScore['message']}

DETECTED ISSUES:
{$issuesText}

RECENT CONSOLE LOGS:
{$recentLogs}

RECOMMENDATIONS:
" . json_encode($recommendations, JSON_PRETTY_PRINT) . "

Please provide a comprehensive analysis with:

1. **Critical Issues**: Address the most severe problems first
2. **Root Cause Analysis**: Explain what's causing these issues
3. **Step-by-Step Solutions**: Provide actionable resolution steps
4. **Prevention Strategies**: How to avoid these issues in the future
5. **Performance Optimization**: Server configuration recommendations

Format your response with clear sections, bullet points, and specific commands where applicable. Prioritize based on issue severity.";
    }

    /**
     * Get context information for different server types
     */
    private function getServerTypeContext(string $serverType): string
    {
        $contexts = [
            'java' => 'This is a Java-based server (Minecraft, etc.). Focus on JVM settings, memory allocation, and Java-specific errors.',
            'nodejs' => 'This is a Node.js application. Focus on npm dependencies, JavaScript errors, and Node.js runtime issues.',
            'python' => 'This is a Python application. Focus on Python modules, syntax errors, and runtime exceptions.',
            'generic' => 'This is a generic server application. Provide general troubleshooting guidance.'
        ];

        return $contexts[$serverType] ?? $contexts['generic'];
    }
}
