<?php

namespace Pterodactyl\Http\Controllers\Api\Client;

use Illuminate\Http\Response;
use Pterodactyl\Models\DailyAdminNotice;
use Pterodactyl\Services\DailyAdminNotices\DailyAdminNoticeRetrievalService;
use Pterodactyl\Transformers\Api\Client\DailyAdminNoticeTransformer;
use Pterodactyl\Http\Requests\Api\Client\DailyAdminNotices\GetDailyAdminNoticesRequest;
use Pterodactyl\Http\Requests\Api\Client\DailyAdminNotices\DismissDailyAdminNoticeRequest;

class DailyAdminNoticeController extends ClientApiController
{
    /**
     * DailyAdminNoticeController constructor.
     */
    public function __construct(private DailyAdminNoticeRetrievalService $retrievalService)
    {
        parent::__construct();
    }

    /**
     * Return all active daily admin notices that should be displayed to the current user.
     */
    public function index(GetDailyAdminNoticesRequest $request): array
    {
        $notices = $this->retrievalService->getNoticesForUser($request->user());

        return $this->fractal->collection($notices)
            ->transformWith($this->getTransformer(DailyAdminNoticeTransformer::class))
            ->toArray();
    }

    /**
     * Dismiss a daily admin notice for the current user.
     */
    public function dismiss(DismissDailyAdminNoticeRequest $request, DailyAdminNotice $notice): Response
    {
        $this->retrievalService->dismissNoticeForUser($notice->id, $request->user());

        return response('', 204);
    }
}
