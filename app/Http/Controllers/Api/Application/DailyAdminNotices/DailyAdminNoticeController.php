<?php

namespace Pterodactyl\Http\Controllers\Api\Application\DailyAdminNotices;

use Illuminate\Http\Response;
use Pterodactyl\Models\DailyAdminNotice;
use Illuminate\Http\JsonResponse;
use Spatie\QueryBuilder\QueryBuilder;
use Pterodactyl\Services\DailyAdminNotices\DailyAdminNoticeUpdateService;
use Pterodactyl\Services\DailyAdminNotices\DailyAdminNoticeCreationService;
use Pterodactyl\Services\DailyAdminNotices\DailyAdminNoticeDeletionService;
use Pterodactyl\Transformers\Api\Application\DailyAdminNoticeTransformer;
use Pterodactyl\Http\Controllers\Api\Application\ApplicationApiController;
use Pterodactyl\Http\Requests\Api\Application\DailyAdminNotices\GetDailyAdminNoticeRequest;
use Pterodactyl\Http\Requests\Api\Application\DailyAdminNotices\GetDailyAdminNoticesRequest;
use Pterodactyl\Http\Requests\Api\Application\DailyAdminNotices\StoreDailyAdminNoticeRequest;
use Pterodactyl\Http\Requests\Api\Application\DailyAdminNotices\DeleteDailyAdminNoticeRequest;
use Pterodactyl\Http\Requests\Api\Application\DailyAdminNotices\UpdateDailyAdminNoticeRequest;

class DailyAdminNoticeController extends ApplicationApiController
{
    /**
     * DailyAdminNoticeController constructor.
     */
    public function __construct(
        private DailyAdminNoticeCreationService $creationService,
        private DailyAdminNoticeDeletionService $deletionService,
        private DailyAdminNoticeUpdateService $updateService
    ) {
        parent::__construct();
    }

    /**
     * Return all the daily admin notices currently registered on the Panel.
     */
    public function index(GetDailyAdminNoticesRequest $request): array
    {
        $notices = QueryBuilder::for(DailyAdminNotice::query())
            ->allowedFilters(['title', 'type', 'is_active'])
            ->allowedSorts(['id', 'title', 'type', 'created_at'])
            ->with('creator:id,username,email')
            ->paginate($request->query('per_page') ?? 50);

        return $this->fractal->collection($notices)
            ->transformWith($this->getTransformer(DailyAdminNoticeTransformer::class))
            ->toArray();
    }

    /**
     * Return a single daily admin notice.
     */
    public function view(GetDailyAdminNoticeRequest $request, DailyAdminNotice $notice): array
    {
        return $this->fractal->item($notice)
            ->transformWith($this->getTransformer(DailyAdminNoticeTransformer::class))
            ->toArray();
    }

    /**
     * Store a new daily admin notice on the Panel and return an HTTP/201 response code with the
     * new notice attached.
     *
     * @throws \Pterodactyl\Exceptions\Model\DataValidationException
     */
    public function store(StoreDailyAdminNoticeRequest $request): JsonResponse
    {
        $notice = $this->creationService->handle($request->validated());

        return $this->fractal->item($notice)
            ->transformWith($this->getTransformer(DailyAdminNoticeTransformer::class))
            ->addMeta([
                'resource' => route('api.application.daily-admin-notices.view', [
                    'notice' => $notice->id,
                ]),
            ])
            ->respond(201);
    }

    /**
     * Update a daily admin notice on the Panel and return the updated record to the user.
     *
     * @throws \Pterodactyl\Exceptions\Model\DataValidationException
     * @throws \Pterodactyl\Exceptions\Repository\RecordNotFoundException
     */
    public function update(UpdateDailyAdminNoticeRequest $request, DailyAdminNotice $notice): array
    {
        $notice = $this->updateService->handle($notice, $request->validated());

        return $this->fractal->item($notice)
            ->transformWith($this->getTransformer(DailyAdminNoticeTransformer::class))
            ->toArray();
    }

    /**
     * Delete a daily admin notice from the Panel.
     */
    public function delete(DeleteDailyAdminNoticeRequest $request, DailyAdminNotice $notice): Response
    {
        $this->deletionService->handle($notice);

        return response('', 204);
    }
}
