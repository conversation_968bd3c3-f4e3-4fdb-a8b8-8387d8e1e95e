<?php

namespace Pterodactyl\Contracts\Repository;

use Pterodactyl\Models\DailyAdminNotice;
use Illuminate\Support\Collection;

interface DailyAdminNoticeRepositoryInterface extends RepositoryInterface
{
    /**
     * Return all active notices that are currently valid and should be shown to a user.
     */
    public function getActiveNoticesForUser(int $userId, string $userRole): Collection;

    /**
     * Return all notices with their creator information.
     */
    public function getAllWithCreator(): Collection;

    /**
     * Return a notice with its creator and dismissal information.
     *
     * @throws \Pterodactyl\Exceptions\Repository\RecordNotFoundException
     */
    public function getWithDetails(int $id): DailyAdminNotice;

    /**
     * Mark a notice as dismissed by a specific user.
     */
    public function dismissForUser(int $noticeId, int $userId): void;

    /**
     * Check if a notice has been dismissed by a specific user.
     */
    public function isDismissedByUser(int $noticeId, int $userId): bool;
}
