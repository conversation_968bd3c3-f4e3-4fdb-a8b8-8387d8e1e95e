<?php

namespace Pterodactyl\Transformers\Api\Client;

use Pterodactyl\Models\DailyAdminNotice;

class DailyAdminNoticeTransformer extends BaseClientTransformer
{
    /**
     * Return the resource name for the JSONAPI output.
     */
    public function getResourceName(): string
    {
        return DailyAdminNotice::RESOURCE_NAME;
    }

    /**
     * Return a generic transformed daily admin notice array for client consumption.
     */
    public function transform(DailyAdminNotice $notice): array
    {
        return [
            'id' => $notice->id,
            'title' => $notice->title,
            'content' => $notice->content,
            'type' => $notice->type,
            'is_dismissible' => $notice->is_dismissible,
            'starts_at' => $this->formatTimestamp($notice->starts_at),
            'expires_at' => $this->formatTimestamp($notice->expires_at),
            'created_at' => $this->formatTimestamp($notice->created_at),
        ];
    }
}
