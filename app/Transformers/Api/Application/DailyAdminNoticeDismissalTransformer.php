<?php

namespace Pterodactyl\Transformers\Api\Application;

use Pterodactyl\Models\DailyAdminNoticeDismissal;
use League\Fractal\Resource\Item;
use League\Fractal\Resource\NullResource;
use Pterodactyl\Services\Acl\Api\AdminAcl;

class DailyAdminNoticeDismissalTransformer extends BaseTransformer
{
    /**
     * List of resources that can be included.
     */
    protected array $availableIncludes = ['user', 'notice'];

    /**
     * Return the resource name for the JSONAPI output.
     */
    public function getResourceName(): string
    {
        return DailyAdminNoticeDismissal::RESOURCE_NAME;
    }

    /**
     * Return a generic transformed daily admin notice dismissal array.
     */
    public function transform(DailyAdminNoticeDismissal $dismissal): array
    {
        return [
            'id' => $dismissal->id,
            'notice_id' => $dismissal->notice_id,
            'user_id' => $dismissal->user_id,
            'dismissed_at' => $this->formatTimestamp($dismissal->dismissed_at),
        ];
    }

    /**
     * Return the user who dismissed the notice.
     *
     * @throws \Pterodactyl\Exceptions\Transformer\InvalidTransformerLevelException
     */
    public function includeUser(DailyAdminNoticeDismissal $dismissal): Item|NullResource
    {
        if (!$this->authorize(AdminAcl::RESOURCE_USERS)) {
            return $this->null();
        }

        $dismissal->loadMissing('user');

        return $this->item($dismissal->getRelation('user'), $this->makeTransformer(UserTransformer::class), 'user');
    }

    /**
     * Return the notice that was dismissed.
     *
     * @throws \Pterodactyl\Exceptions\Transformer\InvalidTransformerLevelException
     */
    public function includeNotice(DailyAdminNoticeDismissal $dismissal): Item|NullResource
    {
        if (!$this->authorize(AdminAcl::RESOURCE_SETTINGS)) {
            return $this->null();
        }

        $dismissal->loadMissing('notice');

        return $this->item($dismissal->getRelation('notice'), $this->makeTransformer(DailyAdminNoticeTransformer::class), 'daily_admin_notice');
    }
}
