<?php

namespace Pterodactyl\Transformers\Api\Application;

use Pterodactyl\Models\DailyAdminNotice;
use League\Fractal\Resource\Item;
use League\Fractal\Resource\Collection;
use League\Fractal\Resource\NullResource;
use Pterodactyl\Services\Acl\Api\AdminAcl;

class DailyAdminNoticeTransformer extends BaseTransformer
{
    /**
     * List of resources that can be included.
     */
    protected array $availableIncludes = ['creator', 'dismissals'];

    /**
     * Return the resource name for the JSONAPI output.
     */
    public function getResourceName(): string
    {
        return DailyAdminNotice::RESOURCE_NAME;
    }

    /**
     * Return a generic transformed daily admin notice array.
     */
    public function transform(DailyAdminNotice $notice): array
    {
        return [
            'id' => $notice->id,
            'title' => $notice->title,
            'content' => $notice->content,
            'type' => $notice->type,
            'target_roles' => $notice->target_roles,
            'is_active' => $notice->is_active,
            'is_dismissible' => $notice->is_dismissible,
            'starts_at' => $this->formatTimestamp($notice->starts_at),
            'expires_at' => $this->formatTimestamp($notice->expires_at),
            'created_by' => $notice->created_by,
            $notice->getUpdatedAtColumn() => $this->formatTimestamp($notice->updated_at),
            $notice->getCreatedAtColumn() => $this->formatTimestamp($notice->created_at),
        ];
    }

    /**
     * Return the user who created this notice.
     *
     * @throws \Pterodactyl\Exceptions\Transformer\InvalidTransformerLevelException
     */
    public function includeCreator(DailyAdminNotice $notice): Item|NullResource
    {
        if (!$this->authorize(AdminAcl::RESOURCE_USERS)) {
            return $this->null();
        }

        $notice->loadMissing('creator');

        return $this->item($notice->getRelation('creator'), $this->makeTransformer(UserTransformer::class), 'user');
    }

    /**
     * Return the dismissals for this notice.
     *
     * @throws \Pterodactyl\Exceptions\Transformer\InvalidTransformerLevelException
     */
    public function includeDismissals(DailyAdminNotice $notice): Collection|NullResource
    {
        if (!$this->authorize(AdminAcl::RESOURCE_USERS)) {
            return $this->null();
        }

        $notice->loadMissing('dismissals.user');

        return $this->collection($notice->getRelation('dismissals'), $this->makeTransformer(DailyAdminNoticeDismissalTransformer::class), 'daily_admin_notice_dismissal');
    }
}
