# Daily Admin Notice Addon - File List

This document lists all files that are part of the Daily Admin Notice addon for Pterodactyl Panel.

## Database Migrations
```
database/migrations/2025_06_30_120000_create_daily_admin_notices_table.php
database/migrations/2025_06_30_120001_create_daily_admin_notice_dismissals_table.php
```

## Backend Models
```
app/Models/DailyAdminNotice.php
app/Models/DailyAdminNoticeDismissal.php
```

## Repository Layer
```
app/Contracts/Repository/DailyAdminNoticeRepositoryInterface.php
app/Repositories/Eloquent/DailyAdminNoticeRepository.php
```

## Service Layer
```
app/Services/DailyAdminNotices/DailyAdminNoticeCreationService.php
app/Services/DailyAdminNotices/DailyAdminNoticeUpdateService.php
app/Services/DailyAdminNotices/DailyAdminNoticeDeletionService.php
app/Services/DailyAdminNotices/DailyAdminNoticeRetrievalService.php
```

## Request Validation Classes
```
app/Http/Requests/Admin/DailyAdminNoticeFormRequest.php
app/Http/Requests/Api/Application/DailyAdminNotices/GetDailyAdminNoticesRequest.php
app/Http/Requests/Api/Application/DailyAdminNotices/GetDailyAdminNoticeRequest.php
app/Http/Requests/Api/Application/DailyAdminNotices/StoreDailyAdminNoticeRequest.php
app/Http/Requests/Api/Application/DailyAdminNotices/UpdateDailyAdminNoticeRequest.php
app/Http/Requests/Api/Application/DailyAdminNotices/DeleteDailyAdminNoticeRequest.php
app/Http/Requests/Api/Client/DailyAdminNotices/GetDailyAdminNoticesRequest.php
app/Http/Requests/Api/Client/DailyAdminNotices/DismissDailyAdminNoticeRequest.php
```

## Controllers
```
app/Http/Controllers/Admin/DailyAdminNoticeController.php
app/Http/Controllers/Api/Application/DailyAdminNotices/DailyAdminNoticeController.php
app/Http/Controllers/Api/Client/DailyAdminNoticeController.php
```

## API Transformers
```
app/Transformers/Api/Application/DailyAdminNoticeTransformer.php
app/Transformers/Api/Application/DailyAdminNoticeDismissalTransformer.php
app/Transformers/Api/Client/DailyAdminNoticeTransformer.php
```

## Frontend Components
```
resources/scripts/components/dashboard/DailyAdminNoticeBar.tsx
resources/scripts/api/dailyAdminNotices.ts
```

## Admin Interface Views
```
resources/views/admin/daily-admin-notices/index.blade.php
resources/views/admin/daily-admin-notices/new.blade.php
resources/views/admin/daily-admin-notices/view.blade.php
```

## Language Files
```
resources/lang/en/admin/daily-admin-notices.php
```

## Documentation and Installation
```
DAILY_ADMIN_NOTICE_README.md
install_daily_admin_notice.sh
ADDON_FILE_LIST.md
```

## Modified Core Files

### Route Files (additions made)
```
routes/admin.php - Added daily admin notice routes
routes/api-application.php - Added application API routes
routes/api-client.php - Added client API routes
```

### Service Provider (additions made)
```
app/Providers/RepositoryServiceProvider.php - Added repository binding
```

### Layout Files (additions made)
```
resources/views/layouts/admin.blade.php - Added navigation menu item
resources/scripts/components/dashboard/DashboardContainer.tsx - Added notice bar component
```

## Installation Notes

### New Files
All files listed above under "Database Migrations", "Backend Models", "Repository Layer", "Service Layer", "Request Validation Classes", "Controllers", "API Transformers", "Frontend Components", "Admin Interface Views", "Language Files", and "Documentation and Installation" are completely new files that need to be added to the Pterodactyl installation.

### Modified Files
The files listed under "Modified Core Files" are existing Pterodactyl files that have been modified to integrate the addon. The specific changes are:

1. **routes/admin.php**: Added route group for daily admin notices
2. **routes/api-application.php**: Added API routes for admin management
3. **routes/api-client.php**: Added API routes for client access
4. **app/Providers/RepositoryServiceProvider.php**: Added repository interface binding
5. **resources/views/layouts/admin.blade.php**: Added menu item in admin navigation
6. **resources/scripts/components/dashboard/DashboardContainer.tsx**: Added notice bar component import and usage

### Installation Process
1. Copy all new files to their respective locations
2. Apply modifications to existing files (or provide patch files)
3. Run database migrations
4. Install/update dependencies
5. Compile frontend assets
6. Clear caches

### Uninstallation Process
1. Remove all new files
2. Revert modifications to existing files
3. Rollback database migrations
4. Recompile frontend assets
5. Clear caches

## Package Structure for Distribution

For marketplace distribution, the addon should be packaged as:

```
daily-admin-notice-addon/
├── README.md (copy of DAILY_ADMIN_NOTICE_README.md)
├── install.sh (copy of install_daily_admin_notice.sh)
├── FILE_LIST.md (this file)
├── app/
│   ├── Models/
│   ├── Services/
│   ├── Http/
│   ├── Contracts/
│   ├── Repositories/
│   └── Transformers/
├── database/
│   └── migrations/
├── resources/
│   ├── scripts/
│   ├── views/
│   └── lang/
└── patches/ (optional - for core file modifications)
    ├── routes_admin.patch
    ├── routes_api_application.patch
    ├── routes_api_client.patch
    ├── RepositoryServiceProvider.patch
    ├── admin_layout.patch
    └── DashboardContainer.patch
```

This structure allows for easy installation and ensures all components are properly organized.
